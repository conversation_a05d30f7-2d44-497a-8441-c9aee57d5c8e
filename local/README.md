# Helm E2E Test Suite

This directory contains an end-to-end test suite for the `ay-chart` Helm chart. The test suite validates various configurations and features of the chart in a real Kubernetes environment using Kind (Kubernetes in Docker).

## Overview

The test suite runs comprehensive tests covering:
- **Deployment configurations** (basic deployment, autoscaling, ingress)
- **StatefulSet configurations** (persistence, health checks, monitoring)
- **Pod settings** (DNS, health checks, Prometheus monitoring)
- **Storage** (PVC enabled/disabled scenarios)
- **Configuration management** (ConfigMaps, environment variables)
- **Node affinity** (node selection and placement)

## Quick Start

### Prerequisites
- Docker and Docker Compose
- Registry credentials (if using private images)

### Basic Usage

```bash
# Run all tests with node labeling
docker-compose run helm-e2e-test -test all -label

# Run all tests with cleanup enabled
docker-compose run helm-e2e-test -test all -label -cleanup

# Run specific tests only
docker-compose run helm-e2e-test -test deployment-basic,dns-enabled
```

## Command Line Options

### Required Options
- **`-test all`** - Run all available test cases
- **`-test <names>`** - Run specific test cases (comma-separated)

### Optional Flags
- **`-label`** - Enable node labeling (required for node affinity tests)
- **`-cleanup`** - Enable cleanup of test namespaces after completion
- **`-debug`** - Enable Helm debug output for troubleshooting
- **`-help`** - Show detailed help and available test names

## Available Test Cases

### Deployment Tests
- `deployment-basic` - Basic deployment configuration
- `dns-enabled` / `dns-disabled` - Pod DNS settings
- `ingress-enabled` / `ingress-disabled` - Ingress configuration
- `autoscaling-enabled` / `autoscaling-disabled` - HPA configuration
- `pvc-enabled` / `pvc-disabled` - Persistent volume claims
- `healthcheck-enabled` / `healthcheck-disabled` - Health check probes
- `prometheus-enabled` / `prometheus-disabled` - Prometheus monitoring
- `configmap-app` / `configmap-debug` / `configmap-unused` - ConfigMap scenarios
- `node-affinity` - Node selection and affinity rules

### StatefulSet Tests
- `statefulset-enabled` - Basic StatefulSet configuration
- `statefulset-pvc` - StatefulSet with persistent storage
- `statefulset-healthcheck` - StatefulSet with health checks
- `statefulset-prometheus` - StatefulSet with Prometheus monitoring
- `statefulset-configmap-unused` - StatefulSet with ConfigMap

## Usage Examples

### Development Workflow
```bash
# Quick test run (no cleanup for faster iteration)
docker-compose run helm-e2e-test -test deployment-basic,dns-enabled

# Full test suite with node labeling
docker-compose run helm-e2e-test -test all -label

# Production-like test (with cleanup)
docker-compose run helm-e2e-test -test all -label -cleanup
```

### Debugging Failed Tests
```bash
# Run with debug output
docker-compose run helm-e2e-test -test deployment-basic -debug

# Run specific failing test without cleanup to inspect resources
docker-compose run helm-e2e-test -test prometheus-enabled
```

### CI/CD Pipeline
```bash
# Recommended for CI: all tests with cleanup
docker-compose run helm-e2e-test -test all -label -cleanup
```

## Environment Variables

Set these environment variables for private registry access:

```bash
export REGISTRY_USERNAME="your-username"
export REGISTRY_PASSWORD="your-password"
export REGISTRY_EMAIL="<EMAIL>"
```

## Test Architecture

### Infrastructure
- **Kind cluster** - Local Kubernetes cluster in Docker
- **Multi-node setup** - Tests node affinity and scheduling
- **Isolated namespaces** - Each test runs in its own namespace

### Test Flow
1. **Cluster Setup** - Kind cluster creation and node preparation
2. **Node Labeling** (optional) - Apply labels for node affinity tests
3. **Test Execution** - Deploy chart with specific configurations
4. **Validation** - Verify resources are created correctly
5. **Cleanup** (optional) - Remove test namespaces and resources

## Troubleshooting

### Common Issues

**Tests not running:**
```bash
# Ensure you specify which tests to run
docker-compose run helm-e2e-test -test all -label
```

**Node affinity tests failing:**
```bash
# Enable node labeling for node affinity tests
docker-compose run helm-e2e-test -test node-affinity -label
```

**Registry authentication errors:**
```bash
# Set registry credentials
export REGISTRY_USERNAME="your-username"
export REGISTRY_PASSWORD="your-password"
```

### Debugging Tips

1. **Use `-debug` flag** for detailed Helm output
2. **Skip `-cleanup`** to inspect resources after test failure
3. **Run specific tests** to isolate issues
4. **Check Kind cluster status**: `kind get clusters`
5. **Inspect namespaces**: `kubectl get namespaces`

## Files Structure

```
local/
├── README.md              # This documentation
├── run-helm-tests.sh      # Main test script
├── entrypoint.sh          # Docker entrypoint
├── kind-config.yaml       # Kind cluster configuration
└── docker-compose.yml     # Docker Compose setup
```

## Contributing

When adding new tests:
1. Add test case to `ALL_TEST_CASES` array in `run-helm-tests.sh`
2. Add corresponding case in `run_all_tests()` function
3. Update this README with the new test description
4. Test the new case in isolation before adding to CI

## Performance Notes

- **Without cleanup**: Tests run faster, useful for development
- **With cleanup**: Slower but ensures clean state, recommended for CI
- **Specific tests**: Much faster than running all tests
- **Node labeling**: Adds ~10 seconds but required for node affinity tests
