{{/*
Expand the name of the chart.
*/}}
{{- define "ay-chart.name" -}}
{{- .Release.Name | trunc 63 | lower | replace "." "-" | trimSuffix "-" }}
{{- end }}

{{/*
Expand the name of the chart.
*/}}
{{- define "ay-chart.uniquename" -}}
{{- $hash := printf "%s-%s" .Release.Name .Release.Namespace -}}
{{-  sha1sum  $hash}}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "ay-chart.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "ay-chart.selectorLabels" -}}
assertive.cc/app: {{ include "ay-chart.name" . }}
{{- end }}

{{- define "ay-chart.podLabels" -}}
{{ range $key, $val := (.Values.podSettings).labels }}
{{ $key }}: {{ $val | quote }}
{{- end}}
{{- end -}}

{{/*
Common labels
*/}}
{{- define "ay-chart.labels" -}}
{{ include "ay-chart.selectorLabels" . }}
app.kubernetes.io/name: {{ include "ay-chart.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
app.kubernetes.io/version: {{ .Chart.AppVersion }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
helm.sh/chart: {{ .Chart.Name }}-{{ .Chart.Version }}
assertive.cc/team: {{ .Values.project.team | quote }}
assertive.cc/project-url: {{ .Values.project.url | quote }}
{{- if eq .Values.nodeSettings.allowOtherAppsOnNode false }}
assertive.cc/block-node: "true"
{{- else }}
assertive.cc/block-node: "false"
{{- end }}
helm.sh/chart: {{ include "ay-chart.chart" . }}
helm.sh/release: {{ .Release.Name }}
{{ include "ay-chart.podLabels" . }}
{{- end }}

{{- define "ay-chart.annotations" -}}
assertive.cc/namespace: {{ .Release.Namespace | quote }}
{{- end }}


{{- define "ay-chart.node-settings-os" -}}
  {{- $nodeOs := "linux" -}}
  {{- range $nodeGroup := .Values.nodeSettings.nodeGroups }}        
    {{- if hasPrefix "windows" $nodeGroup -}}
      {{- $nodeOs = "windows" -}}
    {{- end -}}
  {{- end -}}
  {{- $nodeOs -}}
{{- end}}

{{- define "ay-chart.node-arch-label" -}}
  {{- $nodeArch := "" -}}
  {{- range $nodeGroup := .Values.nodeSettings.nodeGroups }} 
    {{- if hasPrefix "windows" $nodeGroup -}}
      {{- $nodeArch = "kubernetes.io/arch: amd64" -}}
    {{- end -}}
  {{- end -}}
  {{- $nodeArch -}}
{{- end}}

{{/*
Node Selector
*/}}
{{- define "ay-chart.nodeSelector" -}}
nodeSelector:
  kubernetes.io/os: {{ include "ay-chart.node-settings-os" . }}
  {{ include "ay-chart.node-arch-label" . }}
{{- end }}

{{/*
Affinity
*/}}
{{- define "ay-chart.affinity" -}}
affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
        - matchExpressions:
            - key: kubernetes.io/arch
              operator: In
              values:
                - amd64
            {{- with .Values.nodeSettings.instanceTypes }}
            - key: node.kubernetes.io/instance-type
              operator: In
              values:
                {{- toYaml . | nindent 16 }}
            {{- end }}
            {{- with .Values.nodeSettings.nodeGroups }}
            - key: node.cluster.x-k8s.io/node-pool
              operator: In
              values:
                {{- toYaml . | nindent 16 }}
            {{- end }}
            {{- with .Values.nodeSettings.os }}
            - key: kubernetes.io/os
              operator: In
              values:
                {{- toYaml . | nindent 16 }}
            {{- end }}


  {{- if or (eq .Values.nodeSettings.nodeRestrictions "differentNodes") (eq .Values.nodeSettings.allowOtherAppsOnNode false) }}
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
    {{- if eq .Values.nodeSettings.nodeRestrictions "differentNodes" }}
      - labelSelector:
          matchExpressions:
          - key: assertive.cc/app
            operator: In
            values:
            - {{ include "ay-chart.name" . }}
        topologyKey: kubernetes.io/hostname
    {{- end }}
    {{- if eq .Values.nodeSettings.allowOtherAppsOnNode false }}
      - labelSelector:
          matchExpressions:
          - key: assertive.cc/block-node
            operator: DoesNotExist
        topologyKey: kubernetes.io/hostname
    {{- end }}
      - labelSelector:
          matchExpressions:
          - key: assertive.cc/block-node
            operator: NotIn
            values:
            - "true"
        topologyKey: kubernetes.io/hostname
  {{- end }}
  {{- if eq .Values.nodeSettings.nodeRestrictions "sameNode" }}
  podAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchExpressions:
          - key: assertive.cc/app
            operator: In
            values:
            - {{ include "ay-chart.name" . }}
        topologyKey: kubernetes.io/hostname
  {{- end }}
{{- end }}

{{/*
Create the name of the ingress
*/}}
{{- define "ay-chart.ingressName" -}}
{{- printf "cilium" }}
{{- end }}

{{/*
DNS Settings
*/}}
{{- define "ay-chart.dns" -}}
{{- with .Values.podSettings.dns }}
  {{- if .enabled }}
dnsPolicy: {{ .policy | default "ClusterFirst" }}
dnsConfig:
  options:
    - name: ndots
      value: {{ .ndots | default "2" | quote }}
{{- end }}
{{- end }}
{{- end }}


{{/*
Generate comma separated hosts
*/}}
{{- define "ay-chart.commaSeparatedHosts" -}}
{{- $commaSeparatedHosts := list -}}
{{- range $host := .Values.deployment.ingress.hosts -}}
{{- $commaSeparatedHosts = append $commaSeparatedHosts (print .host) -}}
{{- end -}}
{{- join "," $commaSeparatedHosts -}}
{{- end }}

{{/*
Init Containers
*/}}
{{- define "ay-chart.initContainers" -}}

{{- if and (.Values.podSettings.beforeStartScripts) (gt (len .Values.podSettings.beforeStartScripts) 0 ) }}
initContainers:
  {{- range .Values.podSettings.beforeStartScripts }}
  - name: {{ .name | quote }}
    image: {{ .image | quote }}
    command:
    {{- range .commands }}
      - {{ . }}
    {{- end }}
  {{- end }}
{{- end }}
{{- end }}

{{/*
Volumes
*/}}

{{- define "ay-chart.volumes" -}}
volumes:
  - name: {{ include "ay-chart.name" . }}
    configMap:
      name: {{ include "ay-chart.name" . }}
{{- end }}

{{/*
Volume Mounts
*/}}
{{- define "ay-chart.volumeMounts" -}}
{{- $configMaps := .Values.deployment.containerSettings.configMaps | default list }}
{{- $printed := false }}
{{- range $cm := $configMaps }}
  {{- if $cm.enabled }}
    {{- if not $printed }}volumeMounts:
    {{- $printed = true }}{{ end }}
  - name: {{ $cm.name }}
    subPath: {{ $cm.key }}
    mountPath: {{ $cm.mountPath }}
  {{- end }}
{{- end }}
{{- end }}



{{/*
Ports
*/}}

{{- define "ay-chart.containerPorts" -}}
{{- if or .Values.deployment.enabled (.Values.containerSettings.prometheusMonitoring).enabled }}
ports:
  - name: http
    protocol: TCP
    containerPort: {{ .Values.deployment.containerPort }}
{{- if (.Values.containerSettings.prometheusMonitoring).enabled }}
{{- if eq .Values.deployment.containerPort .Values.containerSettings.prometheusMonitoring.port }}
  - name: metrics
    protocol: TCP
    containerPort: {{ .Values.containerSettings.prometheusMonitoring.port }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Ports
*/}}

{{- define "ay-chart.servicePorts" -}}
{{- if or .Values.deployment.enabled }}
ports:
  - port: 80
    name: http
    protocol: TCP
    targetPort: "http"
  - port: 1234
    name: metrics
    protocol: TCP
    targetPort: "metrics"
{{- end }}
{{- end }}

{{/*
Metrics port
*/}}

{{- define "ay-chart.prometheusMonitoringPort" -}}
{{- if eq .Values.deployment.containerPort (.Values.containerSettings.prometheusMonitoring).port }}
{{- print "http" }}
{{- else }}
{{- print "metrics" }}
{{- end }}
{{- end }}

{{/*
Common Metadata
*/}}

{{- define "ay-chart.commonMetadata" -}}
namespace: {{ .Release.Namespace }}
labels:
  {{ include "ay-chart.labels" . | nindent 2 }}
{{- end }}

{{/*
Unified Pod Specs
*/}}

{{- define "ay-chart.podSpecs" -}}
serviceAccountName: {{ template "ay-chart.name" . }}
{{ include "ay-chart.dns" . }}
# Node Selector
{{ include "ay-chart.nodeSelector" . }}
# Affinity
{{ include "ay-chart.affinity" . }}
# Pod Security Context
{{- if .Values.podSettings.securityContext}}
securityContext:
  {{- toYaml .Values.podSettings.securityContext | nindent 2 }}
{{- end }}
{{ include "ay-chart.initContainers" . }}
{{ include "ay-chart.volumes" . }}
{{- end }}

{{/*
Unified Container Specs
*/}}

{{- define "ay-chart.containerSpecs" -}}
{{ include "ay-chart.volumeMounts" .}}
{{- if .Values.deployment.containerSettings.securityContext }}
securityContext:
  {{- toYaml .Values.deployment.containerSettings.securityContext | nindent 2 }}
{{- end }}
image: "{{ .Values.deployment.containerSettings.image.repository }}:{{ .Values.deployment.containerSettings.image.tag }}"
imagePullPolicy: {{ .Values.deployment.containerSettings.image.pullPolicy }}
env:
{{- range $key, $value :=  .Values.deployment.containerSettings.env }}
  - name: {{ $key | upper | replace "-" "_" }}
    value: {{ $value | quote }}
{{- end }}
resources:
  {{- toYaml .Values.containerSettings.resources | nindent 2 }}
args: [
  {{ range .Values.podSettings.containerArgs }}
  {{ . }}
  {{ end }}
]
{{- end }}

{{- define "ay-chart.pvc.metadata.deployment" -}}
name: {{ .Release.Name }}-storage
labels:
  {{- include "ay-chart.labels" . | nindent 2 }}
{{- end }}

{{- define "ay-chart.pvc.spec.deployment" -}}
accessModes:
  {{- toYaml .Values.deployment.containerSettings.persistence.accessModes | nindent 2 }}
resources:
  requests:
    storage: {{ .Values.deployment.containerSettings.persistence.size }}
{{- if .Values.deployment.containerSettings.persistence.storageClassName }}
storageClassName: {{ .Values.deployment.containerSettings.persistence.storageClassName }}
{{- end }}
{{- end }}

# Full name of the chart
{{- define "ay-chart.fullname" -}}
{{- if .Values.fullnameOverride }}
{{ .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}


