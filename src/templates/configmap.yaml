{{- if .Values.deployment.enabled }}
{{- range $index, $configMap := .Values.deployment.containerSettings.configMaps }}
{{- if $configMap.enabled }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $configMap.name | default (printf "%s-config-%d" $.Release.Name $index) }}
  {{- include "ay-chart.commonMetadata" $ | nindent 2 }}
data:
  {{ $configMap.key | default "config.yaml" }}: |
    {{- $configMap.content | default "# Default config content" | toString | nindent 4 }}
{{- end }}
{{- end }}
{{- end }}

{{- if .Values.statefulset.enabled }}
{{- range .Values.statefulset.containerSettings.configMaps }}
{{- if and . .enabled .name .key .content }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .name }}
  {{- include "ay-chart.commonMetadata" $ | nindent 2 }}
data:
  {{ .key }}: |
    {{- .content | toString | nindent 4 }}
{{- end }}
{{- end }}
{{- end }}
